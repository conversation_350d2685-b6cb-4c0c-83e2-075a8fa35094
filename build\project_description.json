{"version": "1.2", "project_name": "esp32_motor_control", "project_version": "1", "project_path": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control", "idf_path": "D:/Espressif/frameworks/esp-idf-v5.5", "build_dir": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build", "config_file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/sdkconfig", "config_defaults": "", "bootloader_elf": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/bootloader/bootloader.elf", "app_elf": "esp32_motor_control.elf", "app_bin": "esp32_motor_control.bin", "build_type": "flash_app", "git_revision": "v5.5", "target": "esp32", "rev": "0", "min_rev": "0", "max_rev": "399", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32-elf-", "c_compiler": "D:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "D:/Espressif/frameworks/esp-idf-v5.5/components/app_trace/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/bt/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/console/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/driver/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_common/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ana_cmpr/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_bitscrambler/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_cam/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gpio/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gptimer/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2c/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2s/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_isp/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_jpeg/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ledc/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_parlio/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_pcnt/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdm/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_spi/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_touch_sens/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_tsens/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_twai/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_uart/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_usb_serial_jtag/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_event/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_client/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_ota/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_server/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_partition/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_pm/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_ringbuf/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_security/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/hal/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/heap/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/ieee802154/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/log/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/mbedtls/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt/esp-mqtt/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_sec_provider/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/openthread/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/pthread/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/soc/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/ulp/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/unity/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/usb/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/vfs/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling/Kconfig;D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader/Kconfig.projbuild;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_app_format/Kconfig.projbuild;D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/Kconfig.projbuild;D:/Espressif/frameworks/esp-idf-v5.5/components/esptool_py/Kconfig.projbuild;D:/Espressif/frameworks/esp-idf-v5.5/components/partition_table/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["app_trace", "app_update", "bootloader", "bootloader_support", "bt", "cmock", "console", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_bitscrambler", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_twai", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_security", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "esptool_py", "fatfs", "freertos", "hal", "heap", "http_parser", "idf_test", "ieee802154", "json", "log", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "perfmon", "protobuf-c", "protocomm", "pthread", "rt", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "xtensa", ""], "build_component_paths": ["D:/Espressif/frameworks/esp-idf-v5.5/components/app_trace", "D:/Espressif/frameworks/esp-idf-v5.5/components/app_update", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt", "D:/Espressif/frameworks/esp-idf-v5.5/components/cmock", "D:/Espressif/frameworks/esp-idf-v5.5/components/console", "D:/Espressif/frameworks/esp-idf-v5.5/components/cxx", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver", "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_app_format", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_bootloader_format", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_common", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ana_cmpr", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_bitscrambler", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_cam", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gpio", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gptimer", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2s", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_isp", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_jpeg", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ledc", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_parlio", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_pcnt", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ppa", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdio", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdm", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdmmc", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdspi", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_spi", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_touch_sens", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_tsens", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_twai", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_uart", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_usb_serial_jtag", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_event", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_client", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_ota", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_server", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_local_ctrl", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif_stack", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_partition", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_pm", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_ringbuf", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_security", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_vfs_console", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump", "D:/Espressif/frameworks/esp-idf-v5.5/components/esptool_py", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal", "D:/Espressif/frameworks/esp-idf-v5.5/components/heap", "D:/Espressif/frameworks/esp-idf-v5.5/components/http_parser", "D:/Espressif/frameworks/esp-idf-v5.5/components/idf_test", "D:/Espressif/frameworks/esp-idf-v5.5/components/ieee802154", "D:/Espressif/frameworks/esp-idf-v5.5/components/json", "D:/Espressif/frameworks/esp-idf-v5.5/components/log", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip", "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/main", "D:/Espressif/frameworks/esp-idf-v5.5/components/mbedtls", "D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_sec_provider", "D:/Espressif/frameworks/esp-idf-v5.5/components/openthread", "D:/Espressif/frameworks/esp-idf-v5.5/components/partition_table", "D:/Espressif/frameworks/esp-idf-v5.5/components/perfmon", "D:/Espressif/frameworks/esp-idf-v5.5/components/protobuf-c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm", "D:/Espressif/frameworks/esp-idf-v5.5/components/pthread", "D:/Espressif/frameworks/esp-idf-v5.5/components/rt", "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash", "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs", "D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport", "D:/Espressif/frameworks/esp-idf-v5.5/components/ulp", "D:/Espressif/frameworks/esp-idf-v5.5/components/unity", "D:/Espressif/frameworks/esp-idf-v5.5/components/usb", "D:/Espressif/frameworks/esp-idf-v5.5/components/vfs", "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant", "D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa", ""], "build_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/app_trace/libapp_trace.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/app_trace/app_trace.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/app_trace/app_trace_util.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/app_trace/host_file_io.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/app_update/libapp_update.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/app_update/esp_ota_ops.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_common_loader.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_clock_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_mem.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_random.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_efuse.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/flash_encrypt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/secure_boot.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_random_esp32.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_utility.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/flash_partitions.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/esp_image_format.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/bootloader_sha.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support/src/esp32/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/bt", "type": "LIBRARY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/bt/libbt.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/bt/controller/esp32/bt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/controller/esp32/hli_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/controller/esp32/hli_vectors.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/btc/core/btc_alarm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/api/esp_blufi_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/hci_log/bt_hci_log.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/btc/core/btc_manage.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/btc/core/btc_task.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/btc/profile/esp/blufi/blufi_prf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/btc/profile/esp/blufi/blufi_protocol.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/alarm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/allocator.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/buffer.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/config.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/fixed_queue.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/pkt_queue.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/fixed_pkt_queue.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/future.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/hash_functions.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/hash_map.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/list.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/mutex.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/thread.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/osi.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/osi/semaphore.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/porting/mem/bt_osi_mem.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/ble_log/ble_log_spi_out.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_a2dp_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_avrc_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_bluedroid_hci.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_bt_device.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_bt_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_gap_ble_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_gap_bt_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_gatt_common_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_gattc_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_gatts_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_hidd_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_hidh_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_hf_ag_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_hf_client_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_spp_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_sdp_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_l2cap_bt_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/api/esp_pbac_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/ar/bta_ar.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_aact.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_ca_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_ca_sm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_cfg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_ci.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_sbc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/av/bta_av_ssm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_cfg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_ci.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_pm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_sco.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/dm/bta_dm_qos.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gatt_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gattc_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gattc_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gattc_cache.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gattc_ci.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gattc_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gattc_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gattc_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gatts_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gatts_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gatts_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gatts_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/gatt/bta_gatts_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hd/bta_hd_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hd/bta_hd_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hd/bta_hd_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hh/bta_hh_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hh/bta_hh_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hh/bta_hh_cfg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hh/bta_hh_le.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hh/bta_hh_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hh/bta_hh_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/jv/bta_jv_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/jv/bta_jv_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/jv/bta_jv_cfg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/jv/bta_jv_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_at.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_cfg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_cmd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_rfc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_sco.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_ag/bta_ag_sdp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_at.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_cmd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_rfc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_sco.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_sdp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/pba/bta_pba_client_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/pba/bta_pba_client_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/pba/bta_pba_client_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/pba/bta_pba_client_sdp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/sdp/bta_sdp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/sdp/bta_sdp_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/sdp/bta_sdp_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/sdp/bta_sdp_cfg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/sys/bta_sys_conn.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/sys/bta_sys_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/bta/sys/utl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_ble_storage.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_config.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_dev.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_dm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_profile_queue.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_sec.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_sm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_storage.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/core/btc_util.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/a2dp/bta_av_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp_control.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp_sink.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp_sink_ext_coedc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp_source.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp_source_ext_codec.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_av.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/avrc/btc_avrc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/avrc/bta_avrc_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/hf_ag/bta_ag_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/hf_ag/btc_hf_ag.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/hf_client/btc_hf_client.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/hf_client/bta_hf_client_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/hid/btc_hd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/hid/btc_hh.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/hid/bta_hh_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/gap/btc_gap_ble.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/gap/btc_gap_bt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/gap/bta_gap_bt_co.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/gatt/btc_gatt_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/gatt/btc_gatt_util.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/gatt/btc_gattc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/gatt/btc_gatts.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/spp/btc_spp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/sdp/btc_sdp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/l2cap/btc_l2cap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/btc/profile/std/pba/btc_pba_client.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/device/bdaddr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/device/controller.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/device/interop.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/alloc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/bitalloc-sbc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/bitalloc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/bitstream-decode.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/decoder-oina.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/decoder-private.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/decoder-sbc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/dequant.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/framing-sbc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/framing.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/oi_codec_version.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/synthesis-8-generated.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/synthesis-dct8.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/decoder/srce/synthesis-sbc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_analysis.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_dct.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_dct_coeffs.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_enc_bit_alloc_mono.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_enc_bit_alloc_ste.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_enc_coeffs.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_encoder.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_packing.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/external/sbc/plc/sbc_plc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/hci/hci_audio.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/hci/hci_hal_h4.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/hci/hci_layer.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/hci/hci_packet_factory.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/hci/hci_packet_parser.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/hci/packet_fragmenter.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/main/bte_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/main/bte_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/a2dp/a2d_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/a2dp/a2d_sbc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avct/avct_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avct/avct_ccb.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avct/avct_l2c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avct/avct_lcb.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avct/avct_lcb_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avdt/avdt_ad.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avdt/avdt_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avdt/avdt_ccb.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avdt/avdt_ccb_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avdt/avdt_l2c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avdt/avdt_msg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avdt/avdt_scb.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avdt/avdt_scb_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avrc/avrc_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avrc/avrc_bld_ct.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avrc/avrc_bld_tg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avrc/avrc_opt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avrc/avrc_pars_ct.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avrc/avrc_pars_tg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avrc/avrc_sdp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/avrc/avrc_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/hid/hidd_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/hid/hidd_conn.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/hid/hidh_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/hid/hidh_conn.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_acl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_addr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_adv_filter.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_batchscan.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_bgconn.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_cont_energy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_gap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_5_gap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_multi_adv.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_ble_privacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_dev.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_devctl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_inq.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_pm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_sco.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btm/btm_sec.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btu/btu_hcif.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btu/btu_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/btu/btu_task.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gap/gap_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gap/gap_ble.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gap/gap_conn.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gap/gap_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/att_protocol.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_attr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_auth.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_cl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_db.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_sr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_sr_hash.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/gatt/gatt_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/goep/goepc_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/goep/goepc_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/hcic/hciblecmds.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/hcic/hcicmds.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2c_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2c_ble.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2c_csm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2c_fcr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2c_link.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2c_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2c_ucd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2c_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/l2cap/l2cap_client.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/obex/obex_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/obex/obex_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/obex/obex_tl_l2cap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/obex/obex_tl_rfcomm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/port_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/port_rfc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/port_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/rfc_l2cap_if.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/rfc_mx_fsm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/rfc_port_fsm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/rfc_port_if.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/rfc_ts_frames.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/rfcomm/rfc_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/sdp/sdp_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/sdp/sdp_db.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/sdp/sdp_discovery.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/sdp/sdp_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/sdp/sdp_server.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/sdp/sdp_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/aes.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/p_256_curvepara.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/p_256_ecc_pp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/p_256_multprecision.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/smp_act.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/smp_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/smp_br_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/smp_cmac.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/smp_keys.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/smp_l2c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/smp_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/stack/smp/smp_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/host/bluedroid/config/stack_config.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/btc/profile/esp/blufi/bluedroid_host/esp_blufi.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/sha256.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/ecc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/ctr_prng.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/ctr_mode.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/aes_decrypt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/aes_encrypt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/ccm_mode.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/ecc_dsa.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/cmac_mode.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/ecc_dh.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/hmac_prng.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/ecc_platform_specific.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/hmac.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/src/cbc_mode.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/bt/common/tinycrypt/port/esp_tinycrypt_port.c"], "include_dirs": ["include/esp32/include", "common/osi/include", "common/api/include/api", "common/btc/profile/esp/blufi/include", "common/btc/profile/esp/include", "common/hci_log/include", "common/ble_log/include", "host/bluedroid/api/include/api", "common/tinycrypt/include", "common/tinycrypt/port"]}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/cmock/libcmock.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/console/libconsole.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/console/commands.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/esp_console_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/esp_console_repl_internal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/split_argv.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/linenoise/linenoise.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/esp_console_repl_chip.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_cmd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_date.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_dbl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_dstr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_end.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_file.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_hashtable.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_int.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_lit.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_rem.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_rex.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_str.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/arg_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/console/argtable3/argtable3.c"], "include_dirs": ["D:/Espressif/frameworks/esp-idf-v5.5/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["esp_system", "pthread"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/cxx/libcxx.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/cxx/cxx_exception_stubs.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/cxx/cxx_guards.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag", "esp_driver_twai"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/driver/libdriver.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/adc_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/adc_dma_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/dac_common_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/esp32/dac_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/timer_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/i2c/i2c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/i2s_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/mcpwm_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/pcnt_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/rmt_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/sigma_delta_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/touch_sensor/touch_sensor_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/touch_sensor/esp32/touch_sensor.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/twai/twai.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/driver/deprecated/adc_i2s_deprecated.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/efuse/libefuse.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/esp32/esp_efuse_table.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/esp32/esp_efuse_fields.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/esp32/esp_efuse_utility.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/src/esp_efuse_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/src/esp_efuse_fields.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/src/esp_efuse_utility.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls/esp_tls.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls/esp_tls_error_capture.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls/esp_tls_platform_port.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm", "esp_driver_i2s", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/adc_oneshot.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/adc_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/adc_cali.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/adc_cali_curve_fitting.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/adc_continuous.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/esp32/adc_dma.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/esp32/adc_cali_line_fitting.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc/deprecated/esp32/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex/src/coexist.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex/src/lib_printf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex/esp32/esp_coex_adapter.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex/src/coexist_debug_diagram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_common/libesp_common.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_bitscrambler": {"alias": "idf::esp_driver_bitscrambler", "target": "___idf_esp_driver_bitscrambler", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_bitscrambler", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_bitscrambler", "reqs": [], "priv_reqs": ["esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_cam/esp_cam_ctlr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac", "type": "LIBRARY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_dac/libesp_driver_dac.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac/dac_oneshot.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac/dac_cosine.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac/dac_continuous.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac/dac_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac/esp32/dac_dma.c"], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gpio/src/gpio.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gpio/src/rtc_io.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gptimer/src/gptimer.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2c/i2c_master.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2c/i2c_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2s/i2s_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2s/i2s_std.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2s/i2s_pdm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2s/i2s_platform.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm", "esp_psram"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": ["esp_driver_gpio"], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/src/mcpwm_cap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/src/mcpwm_com.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/src/mcpwm_fault.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/src/mcpwm_gen.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/src/mcpwm_oper.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/src/mcpwm_sync.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm", "esp_driver_bitscrambler"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_bitscrambler", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt/src/rmt_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt/src/rmt_encoder.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt/src/rmt_encoder_bytes.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt/src/rmt_encoder_copy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt/src/rmt_encoder_simple.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt/src/rmt_rx.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdio", "type": "LIBRARY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_sdio/libesp_driver_sdio.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdio/src/sdio_slave.c"], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdspi/src/sdspi_crc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdspi/src/sdspi_host.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_spi/src/gpspi/spi_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_spi/src/gpspi/spi_master.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_spi/src/gpspi/spi_slave.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_spi/src/gpspi/spi_dma.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_touch_sens", "type": "LIBRARY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_touch_sens/libesp_driver_touch_sens.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_touch_sens/common/touch_sens_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_touch_sens/hw_ver1/touch_version_specific.c"], "include_dirs": ["include", "hw_ver1/include"]}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_tsens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_twai": {"alias": "idf::esp_driver_twai", "target": "___idf_esp_driver_twai", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_twai", "type": "LIBRARY", "lib": "__idf_esp_driver_twai", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_twai/libesp_driver_twai.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_twai/esp_twai.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_twai/esp_twai_onchip.c"], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_uart/src/uart.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_uart/src/uart_wakeup.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_usb_serial_jtag", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/esp_eth.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/esp_eth_netif_glue.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/mac/esp_eth_mac_esp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/mac/esp_eth_mac_esp_dma.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/mac/esp_eth_mac_esp_gpio.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/phy/esp_eth_phy_generic.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/phy/esp_eth_phy_dp83848.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/phy/esp_eth_phy_ip101.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/phy/esp_eth_phy_ksz80xx.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/phy/esp_eth_phy_lan87xx.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth/src/phy/esp_eth_phy_rtl8201.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_event/libesp_event.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_event/default_event_loop.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_event/esp_event.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub/src/gdbstub.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub/src/gdbstub_transport.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub/src/packet.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid/src/esp_hidd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid/src/esp_hidh.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid/src/esp_hid_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid/src/ble_hidd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid/src/ble_hidh.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid/src/bt_hidh.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid/src/bt_hidd.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_client/esp_http_client.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_client/lib/http_auth.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_client/lib/http_header.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server/src/httpd_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server/src/httpd_parse.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server/src/httpd_sess.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server/src/httpd_txrx.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server/src/httpd_uri.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server/src/httpd_ws.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_bootloader_format", "esp_app_format", "esp_event", "esp_partition"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/cpu.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/esp_cpu_intr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/esp_memory_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/cpu_region_protect.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/esp_clk.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/clk_ctrl_os.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/hw_random.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/intr_alloc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/mac_addr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/periph_ctrl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/revision.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/rtc_module.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/regi2c_ctrl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/esp_gpio_reserve.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/sar_periph_ctrl_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/io_mux.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/esp_clk_tree.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/dma/esp_dma_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/dma/gdma_link.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/spi_bus_lock.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/clk_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp_clk_tree_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/spi_share_hw_ctrl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/adc_share_hw_ctrl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/sleep_modem.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/sleep_modes.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/sleep_console.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/sleep_usb.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/sleep_gpio.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/sleep_event.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/rtc_wdt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/mspi_timing_tuning/mspi_timing_tuning.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/sleep_wake_stub.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/esp_clock_output.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/power_supply/brownout.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/rtc_clk.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/rtc_clk_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/rtc_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/rtc_sleep.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/rtc_time.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/chip_info.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/cache_sram_mmu.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/port/esp32/sar_periph_ctrl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support/lowpower"], "include_dirs": ["include", "include/soc", "include/soc/esp32", "dma/include", "ldo/include", "debug_probe/include", "mspi_timing_tuning/include", "mspi_timing_tuning/tuning_scheme_impl/include", "power_supply/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/src/esp_lcd_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/src/esp_lcd_panel_io.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/src/esp_lcd_panel_st7789.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/src/esp_lcd_panel_ops.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd/i80/esp_lcd_panel_io_i2s.c"], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c", "esp_netif"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_local_ctrl/src/esp_local_ctrl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_local_ctrl/src/esp_local_ctrl_transport_ble.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm/esp_mmu_map.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm/port/esp32/ext_mem_layout.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm/esp_cache_msync.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm/esp_cache_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm/cache_esp32.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/esp_netif_handlers.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/esp_netif_objects.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/esp_netif_defaults.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/lwip/esp_netif_lwip.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/lwip/esp_netif_sntp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/lwip/netif/wlanif.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/lwip/netif/ethernetif.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_partition/partition.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "esp_driver_gpio", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy/src/phy_override.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy/src/lib_printf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy/src/phy_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy/src/phy_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy/esp32/phy_init_data.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_pm/pm_locks.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_pm/pm_trace.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram", "type": "LIBRARY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm", "bootloader_support", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_psram/libesp_psram.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram/system_layer/esp_psram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram/esp32/esp_psram_extram_cache.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram/esp32/esp_himem.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram/esp32/esp_psram_impl_quad.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram/device"], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/patches/esp_rom_sys.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/patches/esp_rom_print.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/patches/esp_rom_crc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/patches/esp_rom_uart.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/patches/esp_rom_spiflash.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/patches/esp_rom_efuse.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/patches/esp_rom_gpio.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom/patches/esp_rom_longjmp.S"], "include_dirs": ["include", "esp32/include", "esp32/include/esp32", "esp32"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_security", "type": "LIBRARY", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_security/libesp_security.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_security/src/init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_security/src/esp_crypto_lock.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_security/src/esp_crypto_periph_clk.c"], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_system/libesp_system.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/esp_err.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/crosscore_int.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/esp_ipc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/esp_system_console.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/freertos_hooks.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/int_wdt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/panic.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/esp_system.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/startup.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/startup_funcs.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/system_time.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/stack_check.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/ubsan.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/xt_wdt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/task_wdt/task_wdt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/cpu_start.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/panic_handler.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/esp_system_chip.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/image_process.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/esp_ipc_isr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/panic_arch.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/expression_with_stack.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/debug_helpers.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/debug_stubs.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/arch/xtensa/trax.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/soc/esp32/highint_hdl.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/soc/esp32/clk.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/soc/esp32/reset_reason.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/soc/esp32/system_internal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system/port/soc/esp32/cache_err_int.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer/src/esp_timer.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer/src/esp_timer_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer/src/ets_timer_legacy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer/src/system_time.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer/src/esp_timer_impl_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer/src/esp_timer_impl_lac.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/src/lib_printf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/src/mesh_event.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/src/smartconfig.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/src/wifi_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/src/wifi_default.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/src/wifi_netif.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/src/wifi_default_ap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/esp32/esp_adapter.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/regulatory/esp_wifi_regulatory.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/core_dump_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/core_dump_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/core_dump_flash.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/core_dump_uart.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/core_dump_elf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/core_dump_binary.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/core_dump_sha.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/core_dump_crc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/fatfs/libfatfs.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/diskio/diskio.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/diskio/diskio_rawflash.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/diskio/diskio_wl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/src/ff.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/src/ffunicode.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/port/freertos/ffsystem.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/diskio/diskio_sdmmc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/vfs/vfs_fat.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/vfs/vfs_fat_sdmmc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/freertos/libfreertos.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/heap_idf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/app_startup.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/port_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/port_systick.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/list.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/queue.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/tasks.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/timers.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/event_groups.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/esp_additions/freertos_compatibility.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/esp_additions/idf_additions_event_groups.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/hal/libhal.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/hal/hal_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/mpu_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/efuse_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/esp32/efuse_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/wdt_hal_iram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/mmu_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/esp32/cache_hal_esp32.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/color_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/spi_flash_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/spi_flash_hal_iram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/spi_flash_encrypt_hal_iram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/esp32/clk_tree_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/uart_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/uart_hal_iram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/gpio_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/rtc_io_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/timer_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/ledc_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/ledc_hal_iram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/i2c_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/i2c_hal_iram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/rmt_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/pcnt_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/mcpwm_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/twai_hal_sja1000.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/i2s_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/sdm_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/sdmmc_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/emac_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/adc_hal_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/adc_oneshot_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/adc_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/mpi_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/sha_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/aes_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/brownout_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/spi_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/spi_hal_iram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/spi_slave_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/spi_slave_hal_iram.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/sdio_slave_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/esp32/touch_sensor_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/touch_sensor_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/touch_sens_hal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/hal/esp32/gpio_hal_workaround.c"], "include_dirs": ["platform_port/include", "esp32/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/heap/libheap.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/heap/heap_caps_base.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/heap/heap_caps.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/heap/heap_caps_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/heap/multi_heap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/heap/tlsf/tlsf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/heap/port/memory_layout_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/heap/port/esp32/memory_layout.c"], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "esp_timer", "soc", "hal", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/json/libjson.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/json/cJSON/cJSON.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["hal", "soc", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/log/liblog.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/os/log_timestamp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/log_timestamp_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/os/log_lock.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/buffer/log_buffers.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/os/util.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/util.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/log_format_text.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/log_print.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/log.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/os/log_write.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/log_level/log_level.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/log_level/tag_log_level/tag_log_level.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/log_level/tag_log_level/linked_list/log_linked_list.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/log/src/log_level/tag_log_level/cache/log_binary_heap.c"], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/lwip/liblwip.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/apps/sntp/sntp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/api_lib.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/api_msg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/err.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/if_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/netbuf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/netdb.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/netifapi.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/sockets.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/api/tcpip.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/apps/sntp/sntp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/def.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/dns.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/inet_chksum.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ip.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/mem.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/memp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/netif.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/pbuf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/raw.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/stats.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/sys.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/tcp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/tcp_in.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/tcp_out.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/timeouts.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/udp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/autoip.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/dhcp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/etharp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/icmp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/igmp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/ip4.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/dhcp6.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/ethip6.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/icmp6.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/inet6.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/ip6.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/mld6.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/core/ipv6/nd6.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ethernet.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/bridgeif.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/bridgeif_fdb.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/slipif.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/auth.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/ccp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/chap-md5.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/chap-new.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/chap_ms.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/demand.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/eap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/ecp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/eui64.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/fsm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/ipcp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/lcp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/magic.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/mppe.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/multilink.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/ppp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/pppapi.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/pppoe.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/pppos.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/upap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/vj.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/port/hooks/tcp_isn_default.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/port/hooks/lwip_default_hooks.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/port/debug/lwip_debug.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/port/sockets_ext.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/port/freertos/sys_arch.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/port/if_index.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/port/acd_dhcp_check.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/port/esp32xx/vfs_lwip.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/apps/ping/esp_ping.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/apps/ping/ping.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/apps/ping/ping_sock.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/main/libmain.a", "sources": ["E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/main/esp32_motor_control.c"], "include_dirs": ["."]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/mqtt/libmqtt.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt/esp-mqtt/mqtt_client.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/newlib/libnewlib.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/abort.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/assert.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/heap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/locks.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/poll.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/pthread.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/random.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/getentropy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/termios.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/stdatomic.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/time.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/sysconf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/realpath.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/scandir.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/syscalls.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/reent_syscalls.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/port/esp_time_impl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/port/xtensa/stdatomic_s32c1i.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/flockfile.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/reent_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/newlib_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib/src/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_api.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_cxx_api.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_item_hash_list.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_page.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_pagemanager.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_storage.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_handle_simple.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_handle_locked.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_partition.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_partition_lookup.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_partition_manager.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_types.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_platform.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_bootloader.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_encrypted_partition.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_bootloader_aes.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash/src/nvs_bootloader_xts_aes.c"], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/perfmon/libperfmon.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/perfmon/xtensa_perfmon_access.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/perfmon/xtensa_perfmon_apis.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/protocomm/libprotocomm.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/common/protocomm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/proto-c/constants.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/proto-c/sec0.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/proto-c/sec1.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/proto-c/sec2.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/proto-c/session.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/transports/protocomm_console.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/transports/protocomm_httpd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/security/security0.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/security/security1.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/security/security2.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/crypto/srp6a/esp_srp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/simple_ble/simple_ble.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm/src/transports/protocomm_ble.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/pthread/libpthread.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/pthread/pthread.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/pthread/pthread_cond_var.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/pthread/pthread_local_storage.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/pthread/pthread_rwlock.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/rt", "type": "LIBRARY", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/rt/librt.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/rt/FreeRTOS_POSIX_mqueue.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/rt/FreeRTOS_POSIX_utils.c"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc/sdmmc_cmd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc/sdmmc_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc/sdmmc_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc/sdmmc_io.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc/sdmmc_mmc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc/sdmmc_sd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/soc/libsoc.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/soc/lldesc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/dport_access_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/interrupts.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/gpio_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/uart_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/dport_access.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/adc_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/emac_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/spi_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/ledc_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/pcnt_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/rmt_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/sdm_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/i2s_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/i2c_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/timer_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/lcd_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/mcpwm_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/mpi_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/sdmmc_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/touch_sensor_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/twai_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/wdt_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/dac_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/rtc_io_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/sdio_slave_periph.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/soc/esp32/power_supply_periph.c"], "include_dirs": ["include", "esp32", "esp32/include", "esp32/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/flash_brownout_hook.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_drivers.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_generic.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_issi.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_mxic.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_gd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_winbond.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_boya.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_mxic_opi.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_chip_th.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/memspi_host_driver.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/cache_utils.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/flash_mmap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/flash_ops.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_wrap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/esp_flash_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/esp_flash_spi_init.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_os_func_app.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/spiffs/libspiffs.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs/spiffs_api.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs/spiffs/src/spiffs_cache.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs/spiffs/src/spiffs_check.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs/spiffs/src/spiffs_gc.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs/spiffs/src/spiffs_hydrogen.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs/spiffs/src/spiffs_nucleus.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport/transport.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport/transport_ssl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport/transport_internal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport/transport_socks_proxy.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/unity/libunity.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/unity/unity/src/unity.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/unity/unity_compat.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/unity/unity_runner.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/unity/unity_utils_freertos.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/unity/unity_utils_cache.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/unity/unity_utils_memory.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/unity/unity_port_esp32.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/usb", "type": "CONFIG_ONLY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/vfs/libvfs.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/vfs/vfs.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/vfs/vfs_eventfd.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/vfs/vfs_semihost.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/vfs/nullfs.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling/Partition.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling/SPI_Flash.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling/WL_Ext_Perf.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling/WL_Ext_Safe.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling/WL_Flash.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling/crc32.cpp", "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/src/wifi_config.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/src/wifi_scan.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/src/wifi_ctrl.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/src/manager.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/src/handlers.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/src/scheme_console.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/src/scheme_softap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning/src/scheme_ble.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/port/os_xtensa.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/port/eloop.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/ap/ap_config.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/ap/ieee802_1x.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/ap/wpa_auth.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/ap/sta_info.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/ap/ieee802_11.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/ap/comeback_token.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/common/sae.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/common/dragonfly.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/common/wpa_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/utils/bitfield.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/aes-siv.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/sha256-kdf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/ccmp.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/aes-gcm.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/crypto_ops.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/dh_group5.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/dh_groups.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/ms_funcs.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/sha256-prf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/sha1-prf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/sha384-prf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/md4-internal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/sha1-tprf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/common/ieee802_11_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/chap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_peap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_tls.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/mschapv2.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_fast.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/rsn_supp/wpa.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/utils/base64.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/utils/common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/utils/ext_password.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/utils/uuid.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/utils/wpabuf.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/utils/wpa_debug.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/utils/json.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/wps/wps.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/wps/wps_attr_build.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/wps/wps_attr_parse.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/wps/wps_attr_process.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/wps/wps_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/wps/wps_dev_attr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/wps/wps_enrollee.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/common/sae_pk.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/rc4.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/des-internal.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/aes-wrap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/aes-unwrap.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/esp-idf/xtensa/libxtensa.a", "sources": ["D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa/eri.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa/xt_trax.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa/xtensa_context.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa/xtensa_intr_asm.S", "D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa/xtensa_intr.c", "D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/esp32/include", "common/osi/include", "common/api/include/api", "common/btc/profile/esp/blufi/include", "common/btc/profile/esp/include", "common/hci_log/include", "common/ble_log/include", "host/bluedroid/api/include/api", "common/tinycrypt/include", "common/tinycrypt/port"]}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/Espressif/frameworks/esp-idf-v5.5/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["esp_system", "pthread"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag", "esp_driver_twai"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/Espressif/frameworks/esp-idf-v5.5/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm", "esp_driver_i2s", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_bitscrambler": {"alias": "idf::esp_driver_bitscrambler", "target": "___idf_esp_driver_bitscrambler", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_bitscrambler", "lib": "__idf_esp_driver_bitscrambler", "reqs": [], "priv_reqs": ["esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm", "esp_psram"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": ["esp_driver_gpio"], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm", "esp_driver_bitscrambler"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_bitscrambler", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "hw_ver1/include"]}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_twai": {"alias": "idf::esp_driver_twai", "target": "___idf_esp_driver_twai", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_twai", "lib": "__idf_esp_driver_twai", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_bootloader_format", "esp_app_format", "esp_event", "esp_partition"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32", "dma/include", "ldo/include", "debug_probe/include", "mspi_timing_tuning/include", "mspi_timing_tuning/tuning_scheme_impl/include", "power_supply/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c", "esp_netif"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "esp_driver_gpio", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm", "bootloader_support", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include", "esp32/include/esp32", "esp32"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_security", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_tee": {"alias": "idf::esp_tee", "target": "___idf_esp_tee", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_tee", "lib": "__idf_esp_tee", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "esp_timer", "soc", "hal", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["hal", "soc", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/Espressif/frameworks/esp-idf-v5.5/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/rt", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32", "esp32/include", "esp32/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/touch_element", "lib": "__idf_touch_element", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "D:/Espressif/frameworks/esp-idf-v5.5/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/main", "lib": "__idf_main", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/gdbinit/symbols", "02_prefix_map": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/gdbinit/prefix_map", "03_py_extensions": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/gdbinit/py_extensions", "04_connect": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32-wrover-kit-3.3v.cfg"}