{"sources": [{"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/CMakeFiles/bootloader"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/CMakeFiles/bootloader.rule"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/CMakeFiles/bootloader-complete.rule"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "E:/03.Codes/10.AWA/10.realitytap/esp32_motor_control/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}